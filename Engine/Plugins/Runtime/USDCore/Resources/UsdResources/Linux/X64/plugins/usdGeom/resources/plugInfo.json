# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "constraintTargetIdentifier": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": "", 
                        "documentation": "Unique identifier within a model's namespace for an matrix-valued attribute representing a constraint target", 
                        "type": "token"
                    }, 
                    "elementSize": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": 1, 
                        "displayGroup": "Primvars", 
                        "documentation": "The number of values in a primvar's value array that must be aggregated for each element on the primitive.", 
                        "type": "int"
                    }, 
                    "inactiveIds": {
                        "appliesTo": [
                            "prims"
                        ], 
                        "type": "int64listop"
                    }, 
                    "interpolation": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": "constant", 
                        "displayGroup": "Primvars", 
                        "documentation": "How a primvar interpolates across a primitive; equivalent to RenderMan's 'class specifier'", 
                        "type": "token"
                    }, 
                    "metersPerUnit": {
                        "appliesTo": [
                            "layers"
                        ], 
                        "default": 0.01, 
                        "displayGroup": "Stage", 
                        "type": "double"
                    }, 
                    "unauthoredValuesIndex": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": -1, 
                        "displayGroup": "Primvars", 
                        "documentation": "The index that represents unauthored values in the indices array of an indexed primvar.", 
                        "type": "int"
                    }, 
                    "upAxis": {
                        "appliesTo": [
                            "layers"
                        ], 
                        "default": "Y", 
                        "displayGroup": "Stage", 
                        "type": "token"
                    }
                }, 
                "Types": {
                    "UsdGeomBasisCurves": {
                        "alias": {
                            "UsdSchemaBase": "BasisCurves"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomCurves"
                        ], 
                        "schemaIdentifier": "BasisCurves", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomBoundable": {
                        "alias": {
                            "UsdSchemaBase": "Boundable"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "schemaIdentifier": "Boundable", 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdGeomCamera": {
                        "alias": {
                            "UsdSchemaBase": "Camera"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "schemaIdentifier": "Camera", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomCapsule": {
                        "alias": {
                            "UsdSchemaBase": "Capsule"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Capsule", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomCapsule_1": {
                        "alias": {
                            "UsdSchemaBase": "Capsule_1"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Capsule_1", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomCone": {
                        "alias": {
                            "UsdSchemaBase": "Cone"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Cone", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomCube": {
                        "alias": {
                            "UsdSchemaBase": "Cube"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Cube", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomCurves": {
                        "alias": {
                            "UsdSchemaBase": "Curves"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Curves", 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdGeomCylinder": {
                        "alias": {
                            "UsdSchemaBase": "Cylinder"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Cylinder", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomCylinder_1": {
                        "alias": {
                            "UsdSchemaBase": "Cylinder_1"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Cylinder_1", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomGprim": {
                        "alias": {
                            "UsdSchemaBase": "Gprim"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "schemaIdentifier": "Gprim", 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdGeomHermiteCurves": {
                        "alias": {
                            "UsdSchemaBase": "HermiteCurves"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomCurves"
                        ], 
                        "schemaIdentifier": "HermiteCurves", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomImageable": {
                        "alias": {
                            "UsdSchemaBase": "Imageable"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaIdentifier": "Imageable", 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdGeomMesh": {
                        "alias": {
                            "UsdSchemaBase": "Mesh"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ], 
                        "schemaIdentifier": "Mesh", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomModelAPI": {
                        "alias": {
                            "UsdSchemaBase": "GeomModelAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "GeomModelAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdGeomMotionAPI": {
                        "alias": {
                            "UsdSchemaBase": "MotionAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "MotionAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdGeomNurbsCurves": {
                        "alias": {
                            "UsdSchemaBase": "NurbsCurves"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomCurves"
                        ], 
                        "schemaIdentifier": "NurbsCurves", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomNurbsPatch": {
                        "alias": {
                            "UsdSchemaBase": "NurbsPatch"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ], 
                        "schemaIdentifier": "NurbsPatch", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomPlane": {
                        "alias": {
                            "UsdSchemaBase": "Plane"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Plane", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomPointBased": {
                        "alias": {
                            "UsdSchemaBase": "PointBased"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "PointBased", 
                        "schemaKind": "abstractTyped"
                    }, 
                    "UsdGeomPointInstancer": {
                        "alias": {
                            "UsdSchemaBase": "PointInstancer"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "PointInstancer", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomPoints": {
                        "alias": {
                            "UsdSchemaBase": "Points"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Points", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomPrimvarsAPI": {
                        "alias": {
                            "UsdSchemaBase": "PrimvarsAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "PrimvarsAPI", 
                        "schemaKind": "nonAppliedAPI"
                    }, 
                    "UsdGeomScope": {
                        "alias": {
                            "UsdSchemaBase": "Scope"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomImageable"
                        ], 
                        "schemaIdentifier": "Scope", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomSphere": {
                        "alias": {
                            "UsdSchemaBase": "Sphere"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomGprim"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Sphere", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomSubset": {
                        "alias": {
                            "UsdSchemaBase": "GeomSubset"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaIdentifier": "GeomSubset", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomTetMesh": {
                        "alias": {
                            "UsdSchemaBase": "TetMesh"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomPointBased"
                        ], 
                        "schemaIdentifier": "TetMesh", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomVisibilityAPI": {
                        "alias": {
                            "UsdSchemaBase": "VisibilityAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Imageable"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "VisibilityAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdGeomXform": {
                        "alias": {
                            "UsdSchemaBase": "Xform"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomXformable"
                        ], 
                        "schemaIdentifier": "Xform", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdGeomXformCommonAPI": {
                        "alias": {
                            "UsdSchemaBase": "XformCommonAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "XformCommonAPI", 
                        "schemaKind": "nonAppliedAPI"
                    }, 
                    "UsdGeomXformable": {
                        "alias": {
                            "UsdSchemaBase": "Xformable"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomImageable"
                        ], 
                        "schemaIdentifier": "Xformable", 
                        "schemaKind": "abstractTyped"
                    }
                }
            }, 
            "LibraryPath": "../../../../../../Source/ThirdParty/USD/Unix/x86_64-unknown-linux-gnu/lib/libusd_usdGeom.so", 
            "Name": "usdGeom", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
