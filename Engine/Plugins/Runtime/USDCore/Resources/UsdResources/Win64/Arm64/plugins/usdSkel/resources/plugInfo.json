# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "SdfMetadata": {
                    "weight": {
                        "appliesTo": [
                            "attributes"
                        ], 
                        "default": 0, 
                        "displayGroup": "BlendShape", 
                        "documentation": "The weight value at which an inbeteen shape is applied.", 
                        "type": "float"
                    }
                }, 
                "Types": {
                    "UsdSkelAnimation": {
                        "alias": {
                            "UsdSchemaBase": "SkelAnimation"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaIdentifier": "SkelAnimation", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdSkelBindingAPI": {
                        "alias": {
                            "UsdSchemaBase": "SkelBindingAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaIdentifier": "SkelBindingAPI", 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "UsdSkelBlendShape": {
                        "alias": {
                            "UsdSchemaBase": "BlendShape"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdTyped"
                        ], 
                        "schemaIdentifier": "BlendShape", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdSkelRoot": {
                        "alias": {
                            "UsdSchemaBase": "SkelRoot"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "SkelRoot", 
                        "schemaKind": "concreteTyped"
                    }, 
                    "UsdSkelSkeleton": {
                        "alias": {
                            "UsdSchemaBase": "Skeleton"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdGeomBoundable"
                        ], 
                        "implementsComputeExtent": true, 
                        "schemaIdentifier": "Skeleton", 
                        "schemaKind": "concreteTyped"
                    }
                }
            }, 
            "LibraryPath": "../../../../../../../../../Binaries/Win64/arm64/usd_usdSkel.dll", 
            "Name": "usdSkel", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "library"
        }
    ]
}
