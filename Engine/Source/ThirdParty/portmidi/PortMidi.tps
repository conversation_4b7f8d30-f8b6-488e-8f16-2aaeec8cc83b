<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>PortMidi v.217</Name>
  <Location>/Engine/Source/ThirdParty/portmidi/</Location>
  <Date>2016-06-14T11:48:36.6906991-04:00</Date>
  <Function>Allows content creators to tune any numerical property of any Object in UE4 at runtime using standard off-the-shelf Midi hardware</Function>
  <Justification>It's a much cleaner and easier to understand API than the built in Windows Midi API and provides some auto-recovery functions for things like input buffer overruns and the like</Justification>
  <Eula>https://sourceforge.net/projects/portmedia/</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/PortMidi_License.txt</LicenseFolder>
</TpsData>