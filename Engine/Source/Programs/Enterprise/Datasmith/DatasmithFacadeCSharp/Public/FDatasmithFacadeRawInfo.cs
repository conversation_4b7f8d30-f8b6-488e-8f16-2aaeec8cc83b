// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeRawInfo : global::System.IDisposable {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;
  protected bool swigCMemOwn;

  internal FDatasmithFacadeRawInfo(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeRawInfo obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  ~FDatasmithFacadeRawInfo() {
    Dispose(false);
  }

  public void Dispose() {
    Dispose(true);
    global::System.GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeRawInfo(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
    }
  }

  public class FDatasmithFacadeDataPointId : global::System.IDisposable {
    private global::System.Runtime.InteropServices.HandleRef swigCPtr;
    protected bool swigCMemOwn;
  
    internal FDatasmithFacadeDataPointId(global::System.IntPtr cPtr, bool cMemoryOwn) {
      swigCMemOwn = cMemoryOwn;
      swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
    }
  
    internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeDataPointId obj) {
      return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
    }
  
    ~FDatasmithFacadeDataPointId() {
      Dispose(false);
    }
  
    public void Dispose() {
      Dispose(true);
      global::System.GC.SuppressFinalize(this);
    }
  
    protected virtual void Dispose(bool disposing) {
      lock(this) {
        if (swigCPtr.Handle != global::System.IntPtr.Zero) {
          if (swigCMemOwn) {
            swigCMemOwn = false;
            DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointId(swigCPtr);
          }
          swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
        }
      }
    }
  
    public string GetName() {
      string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointId_GetName(swigCPtr));
      return ret;
    }
  
    public FDatasmithFacadeGuid GetId() {
      FDatasmithFacadeGuid ret = new FDatasmithFacadeGuid(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointId_GetId(swigCPtr), true);
      return ret;
    }
  
    public bool IsPublic() {
      bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointId_IsPublic(swigCPtr);
      return ret;
    }
  
  }

  public class FDatasmithFacadeEndpointInfo : global::System.IDisposable {
    private global::System.Runtime.InteropServices.HandleRef swigCPtr;
    protected bool swigCMemOwn;
  
    internal FDatasmithFacadeEndpointInfo(global::System.IntPtr cPtr, bool cMemoryOwn) {
      swigCMemOwn = cMemoryOwn;
      swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
    }
  
    internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeEndpointInfo obj) {
      return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
    }
  
    ~FDatasmithFacadeEndpointInfo() {
      Dispose(false);
    }
  
    public void Dispose() {
      Dispose(true);
      global::System.GC.SuppressFinalize(this);
    }
  
    protected virtual void Dispose(bool disposing) {
      lock(this) {
        if (swigCPtr.Handle != global::System.IntPtr.Zero) {
          if (swigCMemOwn) {
            swigCMemOwn = false;
            DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo(swigCPtr);
          }
          swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
        }
      }
    }
  
    public string GetName() {
      string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetName(swigCPtr));
      return ret;
    }
  
    public int GetNumberOfDestinations() {
      int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetNumberOfDestinations(swigCPtr);
      return ret;
    }
  
    public FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointId GetDestination(int Index) {
      global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetDestination(swigCPtr, Index);
      FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointId ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointId(cPtr, false);
      return ret;
    }
  
    public int GetNumberOfSources() {
      int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetNumberOfSources(swigCPtr);
      return ret;
    }
  
    public FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointId GetSource(int Index) {
      global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetSource(swigCPtr, Index);
      FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointId ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointId(cPtr, false);
      return ret;
    }
  
    public string GetUserName() {
      string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetUserName(swigCPtr));
      return ret;
    }
  
    public string GetExecutableName() {
      string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetExecutableName(swigCPtr));
      return ret;
    }
  
    public string GetComputerName() {
      string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetComputerName(swigCPtr));
      return ret;
    }
  
    public bool IsLocal() {
      bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_IsLocal(swigCPtr);
      return ret;
    }
  
    public uint GetProcessId() {
      uint ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeEndpointInfo_GetProcessId(swigCPtr);
      return ret;
    }
  
  }

  public class FDatasmithFacadeDataPointInfo : global::System.IDisposable {
    private global::System.Runtime.InteropServices.HandleRef swigCPtr;
    protected bool swigCMemOwn;
  
    internal FDatasmithFacadeDataPointInfo(global::System.IntPtr cPtr, bool cMemoryOwn) {
      swigCMemOwn = cMemoryOwn;
      swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
    }
  
    internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeDataPointInfo obj) {
      return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
    }
  
    ~FDatasmithFacadeDataPointInfo() {
      Dispose(false);
    }
  
    public void Dispose() {
      Dispose(true);
      global::System.GC.SuppressFinalize(this);
    }
  
    protected virtual void Dispose(bool disposing) {
      lock(this) {
        if (swigCPtr.Handle != global::System.IntPtr.Zero) {
          if (swigCMemOwn) {
            swigCMemOwn = false;
            DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo(swigCPtr);
          }
          swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
        }
      }
    }
  
    public FDatasmithFacadeMessageAddress GetEndpointAddress() {
      FDatasmithFacadeMessageAddress ret = new FDatasmithFacadeMessageAddress(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_GetEndpointAddress(swigCPtr), true);
      return ret;
    }
  
    public string GetName() {
      string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_GetName(swigCPtr));
      return ret;
    }
  
    public bool IsSource() {
      bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_IsSource(swigCPtr);
      return ret;
    }
  
    public bool IsOnThisEndpoint() {
      bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_IsOnThisEndpoint(swigCPtr);
      return ret;
    }
  
    public bool IsPublic() {
      bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeDataPointInfo_IsPublic(swigCPtr);
      return ret;
    }
  
  }

  public class FDatasmithFacadeStreamInfo : global::System.IDisposable {
    private global::System.Runtime.InteropServices.HandleRef swigCPtr;
    protected bool swigCMemOwn;
  
    internal FDatasmithFacadeStreamInfo(global::System.IntPtr cPtr, bool cMemoryOwn) {
      swigCMemOwn = cMemoryOwn;
      swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
    }
  
    internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeStreamInfo obj) {
      return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
    }
  
    ~FDatasmithFacadeStreamInfo() {
      Dispose(false);
    }
  
    public void Dispose() {
      Dispose(true);
      global::System.GC.SuppressFinalize(this);
    }
  
    protected virtual void Dispose(bool disposing) {
      lock(this) {
        if (swigCPtr.Handle != global::System.IntPtr.Zero) {
          if (swigCMemOwn) {
            swigCMemOwn = false;
            DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo(swigCPtr);
          }
          swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
        }
      }
    }
  
    public uint GetStreamId() {
      uint ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo_GetStreamId(swigCPtr);
      return ret;
    }
  
    public FDatasmithFacadeGuid GetSource() {
      FDatasmithFacadeGuid ret = new FDatasmithFacadeGuid(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo_GetSource(swigCPtr), true);
      return ret;
    }
  
    public FDatasmithFacadeGuid GetDestination() {
      FDatasmithFacadeGuid ret = new FDatasmithFacadeGuid(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo_GetDestination(swigCPtr), true);
      return ret;
    }
  
    public bool IsActive() {
      bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_FDatasmithFacadeStreamInfo_IsActive(swigCPtr);
      return ret;
    }
  
  }

  public FDatasmithFacadeMessageAddress GetThisEndpointAddress() {
    FDatasmithFacadeMessageAddress ret = new FDatasmithFacadeMessageAddress(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_GetThisEndpointAddress(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeRawInfo.FDatasmithFacadeEndpointInfo GetEndpointInfo(FDatasmithFacadeMessageAddress MessageAddress) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_GetEndpointInfo(swigCPtr, FDatasmithFacadeMessageAddress.getCPtr(MessageAddress));
    FDatasmithFacadeRawInfo.FDatasmithFacadeEndpointInfo ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeRawInfo.FDatasmithFacadeEndpointInfo(cPtr, false);
    return ret;
  }

  public FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointInfo GetDataPointsInfo(FDatasmithFacadeGuid Id) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_GetDataPointsInfo(swigCPtr, FDatasmithFacadeGuid.getCPtr(Id));
    FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointInfo ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeRawInfo.FDatasmithFacadeDataPointInfo(cPtr, false);
    return ret;
  }

  public int GetNumberOfStreamsInfo() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_GetNumberOfStreamsInfo(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeRawInfo.FDatasmithFacadeStreamInfo GetStreamInfo(int Index) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeRawInfo_GetStreamInfo(swigCPtr, Index);
    FDatasmithFacadeRawInfo.FDatasmithFacadeStreamInfo ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeRawInfo.FDatasmithFacadeStreamInfo(cPtr, false);
    return ret;
  }

}
