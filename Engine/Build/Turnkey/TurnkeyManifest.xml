<?xml version="1.0" encoding="utf-8" ?>
<TurnkeyManifest>
	<AdditionalManifests>
		<Manifest>file:$(EngineDir)/Platforms/*/Build/Turnkey/TurnkeyManifest.xml</Manifest>
		<Manifest>$(ThisManifestDir)/Licensee_TurnkeyManifest.xml</Manifest>
		<Manifest>file:$(EngineDir)/Restricted/NotForLicensees/Build/Turnkey/TurnkeyManifest.xml</Manifest>
		<Manifest>file:$(EngineDir)/Restricted/NoRedist/Build/Turnkey/TurnkeyManifest.xml</Manifest>
		<Manifest>$(UE_STUDIO_TURNKEY_LOCATION)</Manifest>
	</AdditionalManifests>

    
    <!-- Public file sources below we can share with all licensees -->
    <FileSource>
        <Platform>Android</Platform>
        <Type>Misc</Type>
        <Name>AndroidStudio</Name>
		<!--  Koala Feature Drop 2024.1.2 Patch 1 September 17, 2024 -->
        <Source HostPlatform="Win64">https://redirector.gvt1.com/edgedl/android/studio/install/2024.1.2.13/android-studio-2024.1.2.13-windows.exe</Source>
        <Source HostPlatform="Linux">https://redirector.gvt1.com/edgedl/android/studio/ide-zips/2024.1.2.13/android-studio-2024.1.2.13-linux.tar.gz</Source>
        <Source HostPlatform="Mac">https://redirector.gvt1.com/edgedl/android/studio/install/2024.1.2.13/android-studio-2024.1.2.13-mac.dmg</Source>
    </FileSource>

</TurnkeyManifest>
